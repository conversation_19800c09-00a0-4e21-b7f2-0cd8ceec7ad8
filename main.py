r"""
Author: li <EMAIL>                # 作者信息及邮箱
Date: 2024-12-18 21:33:04                  # 创建日期
LastEditors: li <EMAIL>           # 最后编辑者信息
LastEditTime: 2025-03-09 22:16:13          # 最后编辑时间
FilePath: \单三相电表测试软件\main.py      # 文件路径
"""

import sys  # 导入系统模块，用于访问与Python解释器和环境相关的变量和函数
import os  # 导入操作系统模块，用于文件路径操作和系统交互
from typing import Callable, TypeVar  # 导入类型注解相关模块
from PySide6.QtWidgets import (
    QApplication,
    QWidget,
    QStackedWidget,
    QPushButton,
    QTextEdit,
)  # 导入PySide6的应用程序和窗口部件类

# 类型别名定义，用于减少类型检查错误
T = TypeVar("T")  # 通用类型变量

# 导入模块路径，加上".."表示上一级目录
sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "ui文件"))
)  # 添加ui文件目录到Python模块搜索路径
sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "software"))
)  # 添加software目录到Python模块搜索路径

from ui文件.主界面_ui import Ui_main_page  # 导入主界面UI类  # noqa: E402
from software.serial_fuc import serial_fuc_s  # 导入串口功能类  # noqa: E402
from software.protocol import DLT645_2007_s  # 导入DLT645-2007协议类
from software.file_handler import file_handler_s  # 导入文件处理类
from stacked_widget import PowerCalibrationWindow, ErrorCalibrationWindow  # noqa: E402


#  界面，注意继承的QWidget，QMainWindow是有自己的布局的，一定要QMainWindow的话需要先删除布局
class MainWindow(QWidget, Ui_main_page):
    """主窗口类，继承自QWidget和Ui_main_page"""

    # UI控件类型注解
    stackedWidget: "QStackedWidget"
    power_cali_btn: "QPushButton"
    err_cali_btn: "QPushButton"
    clear_log_btn: "QPushButton"
    log_edit: "QTextEdit"

    # 自定义组件类型注解
    power_cali_window: "PowerCalibrationWindow"
    err_cali_window: "ErrorCalibrationWindow"
    serial_handler: "serial_fuc_s"
    protocol_handler: "DLT645_2007_s"
    file_handler: "file_handler_s"

    # 方法引用类型注解
    display_to_log_edit: Callable[[str, str], None]
    write_log_message: Callable[[str], None]

    def __init__(self):
        """初始化主窗口"""
        super(MainWindow, self).__init__()
        self.setupUi(self)  # 设置UI，从Ui_main_page继承的方法

        # 创建不同的面
        self.power_cali_window = PowerCalibrationWindow(self)
        self.err_cali_window = ErrorCalibrationWindow(self)

        # 添加页面到折叠窗口
        self.stackedWidget.addWidget(self.power_cali_window)
        self.stackedWidget.addWidget(self.err_cali_window)

        # 创建处理器
        self.serial_handler = serial_fuc_s(self)
        self.display_to_log_edit = self.serial_handler.write_data_to_log_edit
        self.protocol_handler = DLT645_2007_s(self)
        self.file_handler = file_handler_s(self)
        self.write_log_message = self.file_handler.write_log_message

        # 连接按钮事件
        self.power_cali_btn.clicked.connect(self.show_power_calibration)
        self.err_cali_btn.clicked.connect(self.show_error_calibration)
        self.clear_log_btn.clicked.connect(self.log_edit.clear)

        # 设置主窗口的最小尺寸
        self.setMinimumSize(900, 800)

        # 默认显示功率校表页面，显示的时候加载校表参数
        self.show_power_calibration()
        self.power_cali_window.get_cali_params_from_json()

        # 设置样式表
        self.setStyleSheet(DARK_THEME_STYLESHEET)

    def show_power_calibration(self) -> None:
        """显示功率校表页面"""
        self.stackedWidget.setCurrentWidget(self.power_cali_window)
        self.power_cali_btn.setStyleSheet("background-color: lightblue;")
        self.err_cali_btn.setStyleSheet("")
        self.display_to_log_edit("显示功率校表页面", "white")

    def show_error_calibration(self) -> None:
        """显示误差校表页面"""
        self.stackedWidget.setCurrentWidget(self.err_cali_window)
        self.err_cali_btn.setStyleSheet("background-color: lightblue;")
        self.power_cali_btn.setStyleSheet("")
        self.display_to_log_edit("显示误差校表页面", "white")

# 黑色主题样式表
DARK_THEME_STYLESHEET = """
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    QComboBox {
        background-color: #3c3f41;
        border: 1px solid #646464;
        border-radius: 3px;
        padding: 1px 18px 1px 3px;
        color: #ffffff;
    }
    QComboBox:hover {
        border: 1px solid #808080;
    }
    QComboBox::drop-down {
        border: 0px;
    }
    QComboBox::down-arrow {
        image: url(down_arrow.png);
        width: 12px;
        height: 12px;
    }
    QPushButton {
        background-color:rgba(54, 79, 128, 0.7);
        border: 1px solid #4b6eaf;
        border-radius: 3px;
        padding: 5px;
        color: #ffffff;
    }
    QPushButton:hover {
        background-color: #4b6eaf;
    }
    QPushButton:pressed {
        background-color: #2d4766;
    }
    QLineEdit {
        background-color: #3c3f41;
        border: 1px solid #646464;
        border-radius: 3px;
        padding: 2px;
        color: #ffffff;
    }
    QLineEdit:focus {
        border: 1px solid #ffffff;
    }
    QTextEdit {
        background-color: #2b2b2b;
        border: 1px solid #646464;
        color: #a9b7c6;
    }
    QLabel {
        color: #a9b7c6;
    }
"""

# 白色主题样式表
LIGHT_THEME_STYLESHEET = """
    QWidget {
        background-color: #ffffff;
        color: #000000;
    }
    QComboBox {
        background-color: #ffffff;
        border: 1px solid #cccccc;
        border-radius: 3px;
        padding: 1px 18px 1px 3px;
        color: #000000;
    }
    QComboBox:hover {
        border: 1px solid #999999;
    }
    QComboBox::drop-down {
        border: 0px;
    }
    QComboBox::down-arrow {
        image: url(down_arrow.png);
        width: 12px;
        height: 12px;
    }
    QPushButton {
        background-color: #f0f0f0;
        border: 1px solid #cccccc;
        border-radius: 3px;
        padding: 5px;
        color: #000000;
    }
    QPushButton:hover {
        background-color: #e0e0e0;
    }
    QPushButton:pressed {
        background-color: #d0d0d0;
    }
    QLineEdit {
        background-color: #ffffff;
        border: 1px solid #cccccc;
        border-radius: 3px;
        padding: 2px;
        color: #000000;
    }
    QLineEdit:focus {
        border: 1px solid #000000;
    }
    QTextEdit {
        background-color: #ffffff;
        border: 1px solid #cccccc;
        color: #000000;
    }
    QLabel {
        color: #000000;
    }
"""

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())





