from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment
from datetime import datetime


class ExcelHandler:
    def __init__(self):
        self.wb = None
        self.ws = None

    def create_new_excel(self, filename="校准数据.xlsx"):
        """创建新的Excel文件"""
        self.wb = Workbook()
        self.ws = self.wb.active
        self.ws.title = "校准数据"

        # 设置表头
        headers = ["日期", "时间", "相位", "电压", "电流", "有功功率", "无功功率", "视在功率"]
        for col, header in enumerate(headers, 1):
            cell = self.ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center")

        self.wb.save(filename)

    def add_calibration_data(
        self, filename, phase, voltage, current, active_power, reactive_power, apparent_power
    ):
        """添加校准数据"""
        try:
            self.wb = load_workbook(filename)
            self.ws = self.wb.active

            # 获取下一个空行
            next_row = self.ws.max_row + 1

            # 获取当前时间
            now = datetime.now()
            date_str = now.strftime("%Y-%m-%d")
            time_str = now.strftime("%H:%M:%S")

            # 添加数据
            row_data = [
                date_str,
                time_str,
                phase,
                voltage,
                current,
                active_power,
                reactive_power,
                apparent_power,
            ]

            for col, value in enumerate(row_data, 1):
                cell = self.ws.cell(row=next_row, column=col)
                cell.value = value
                cell.alignment = Alignment(horizontal="center")

            self.wb.save(filename)
            return True
        except Exception as e:
            print(f"保存数据时发生错误: {str(e)}")
            return False

    def read_last_data(self, filename):
        """读取最后一行数据"""
        try:
            self.wb = load_workbook(filename)
            self.ws = self.wb.active

            last_row = self.ws.max_row
            if last_row <= 1:  # 只有表头
                return None

            data = {}
            headers = [cell.value for cell in self.ws[1]]
            values = [cell.value for cell in self.ws[last_row]]

            for header, value in zip(headers, values):
                data[header] = value

            return data
        except Exception as e:
            print(f"读取数据时发生错误: {str(e)}")
            return None
