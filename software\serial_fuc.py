r"""
Author: li <EMAIL>
Date: 2024-12-15 22:55:12
LastEditors: li <EMAIL>
LastEditTime: 2025-03-09 22:16:13
FilePath: \单三相电表测试软件\software\serial_fuc.py
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Dict, Any, Optional
from datetime import datetime
from PySide6.QtWidgets import QMessageBox
from PySide6.QtSerialPort import QSerialPort, QSerialPortInfo
from PySide6.QtCore import QByteArray

if TYPE_CHECKING:
    from ui文件.主界面_ui import Ui_main_page

class serial_fuc_s:
    def __init__(self, parent: "Ui_main_page"):
        try:
            if parent is None:
                raise ValueError("parent cannot be None")

            self.parent = parent
            self.serial_port = QSerialPort()
            self._received_data = QByteArray()

            # 连接信号和初始化
            self.serial_port.readyRead.connect(self.process_received_data)
            self.refresh_serial()

            # 安全连接按钮信号
            if hasattr(self.parent, "refresh_btn"):
                self.parent.refresh_btn.clicked.connect(self.refresh_serial)
            if hasattr(self.parent, "open_btn"):
                self.parent.open_btn.clicked.connect(self.handle_serial_connection)
            if hasattr(self.parent, "set_serial_1_btn"):
                self.parent.set_serial_1_btn.clicked.connect(self.set_serial_115200)
            if hasattr(self.parent, "set_serial_2_btn"):
                self.parent.set_serial_2_btn.clicked.connect(self.set_serial_2400)

        except Exception as e:
            QMessageBox.critical(
                parent,
                "错误",
                f"串口初始化失败: {str(e)}",
                QMessageBox.StandardButton.Ok,
                QMessageBox.StandardButton.Close,
            )

    def process_received_data(self) -> None:
        """处理接收到的数据"""
        if not self.serial_port or not self.serial_port.isOpen():
            return

        data = self.serial_port.readAll().data()
        if not data:
            return

        try:
            hex_data = " ".join(f"{x:02X}" for x in data)
            self.write_data_to_log_edit(f"接收: {hex_data}", "green")

            if not hasattr(self.parent, "protocol_handler"):
                raise AttributeError("缺少 protocol_handler 属性")

            response: Dict[str, Any] = self.parent.protocol_handler.dlt645_parse_response(data)
            if isinstance(response, dict) and len(response) == 3:
                response["cmd_code"] = response["cmd_code"] + 0x80
                response["data"] = b"\x00\x00"
                send_data = self.parent.protocol_handler.dlt645_build_cmd(response)
                self.send_response(send_data)
            elif isinstance(response, str):
                self.write_data_to_log_edit(f"错误类型：{response}", "red")
            else:
                self.write_data_to_log_edit(f"未知类型：{response}", "red")

        except Exception as e:
            self.write_data_to_log_edit(f"数据处理错误: {str(e)}", "red")

    def send_response(self, response: bytes) -> None:
        """发送响应数据"""
        try:
            if self.serial_port and self.serial_port.isOpen():
                self.serial_port.write(response)
                log_str = "".join(f"{x:02X} " for x in response)
                self.write_data_to_log_edit(f"发送: {log_str}", "blue")
        except Exception as e:
            self.write_data_to_log_edit(f"发送错误: {str(e)}", "red")

    def refresh_serial(self) -> None:
        """刷新可用串口列表"""
        if not hasattr(self.parent, "port_box"):
            return

        port_list = list(QSerialPortInfo.availablePorts())
        self.parent.port_box.clear()
        self.parent.port_box.addItems([port.portName() for port in port_list])

    def open_serial(self) -> Optional[QSerialPort]:
        """打开串口"""
        try:
            if not hasattr(self.parent, "port_box"):
                raise AttributeError("缺少 port_box 属性")

            port_name = self.parent.port_box.currentText()
            if not port_name:
                QMessageBox.critical(
                    self.parent, "错误", "请选择串口", QMessageBox.StandardButton.Ok , QMessageBox.StandardButton.Close
                )
                return None

            self.serial_port.setPortName(port_name)
            self.serial_port.setBaudRate(int(self.parent.band_rate_box.currentText()))
            self.serial_port.setDataBits(QSerialPort.Data8)
            self.serial_port.setParity(QSerialPort.NoParity)
            self.serial_port.setStopBits(QSerialPort.OneStop)

            if self.serial_port.open(QSerialPort.ReadWrite):
                return self.serial_port
            else:
                QMessageBox.critical(
                    self.parent,
                    "错误",
                    f"无法打开串口: {self.serial_port.errorString()}",
                    QMessageBox.StandardButton.Ok , QMessageBox.StandardButton.Close,
                )
                return None

        except Exception as e:
            QMessageBox.critical(
                self.parent,
                "错误",
                f"打开串口失败: {str(e)}",
                QMessageBox.StandardButton.Ok , QMessageBox.StandardButton.Close,
            )
            return None

    def close_serial(self) -> None:
        """关闭串口"""
        if self.serial_port and self.serial_port.isOpen():
            self.serial_port.close()

    def handle_serial_connection(self) -> None:
        """处理串口连接状态切换"""
        if not self.serial_port.isOpen():
            self.serial_port = self.open_serial()
            if self.serial_port:
                self.parent.open_btn.setText("关闭串口")
                self.disable_serial_controls()
        else:
            self.close_serial()
            self.parent.open_btn.setText("打开串口")
            self.enable_serial_controls()

    def set_serial_115200(self) -> None:
        """设置串口1参数"""
        if hasattr(self.parent, "band_rate_box"):
            self.parent.band_rate_box.setCurrentText("115200")
            self.parent.data_len_box.setCurrentText("8位")
            self.parent.even_box.setCurrentText("无校验")
            self.parent.stop_bits_box.setCurrentText("1")

    def set_serial_2400(self) -> None:
        """设置串口2参数"""
        if hasattr(self.parent, "band_rate_box"):
            self.parent.band_rate_box.setCurrentText("2400")
            self.parent.data_len_box.setCurrentText("8位")
            self.parent.even_box.setCurrentText("偶校验")
            self.parent.stop_bits_box.setCurrentText("1")

    def disable_serial_controls(self) -> None:
        """禁用串口控件"""
        controls = [
            "port_box",
            "band_rate_box",
            "data_len_box",
            "even_box",
            "stop_bits_box",
            "set_serial_1_btn",
            "set_serial_2_btn",
            "refresh_btn",
        ]
        for control in controls:
            if hasattr(self.parent, control):
                getattr(self.parent, control).setEnabled(False)

    def enable_serial_controls(self) -> None:
        """启用串口控件"""
        controls = [
            "port_box",
            "band_rate_box",
            "data_len_box",
            "even_box",
            "stop_bits_box",
            "set_serial_1_btn",
            "set_serial_2_btn",
            "refresh_btn",
        ]
        for control in controls:
            if hasattr(self.parent, control):
                getattr(self.parent, control).setEnabled(True)

    def write_data_to_log_edit(self, data: str, color: str = "green", align: str = "left") -> None:
        """写入日志"""
        if not hasattr(self.parent, "log_edit"):
            return

        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S.%f")[:-3] + "] "
        html_text = f'<div style="text-align: {align};"><span style="color: gray;">{timestamp}</span><span style="color: {color};">{data}</span></div>'
        self.parent.log_edit.append(html_text)
        self.parent.log_edit.ensureCursorVisible()

    def enable_serial_controls(self) -> None:
        """启用串口设置相关控件"""
        self.parent.port_box.setEnabled(True)
        self.parent.band_rate_box.setEnabled(True)
        self.parent.data_len_box.setEnabled(True)
        self.parent.even_box.setEnabled(True)
        self.parent.stop_bits_box.setEnabled(True)
        self.parent.set_serial_1_btn.setEnabled(True)
        self.parent.set_serial_2_btn.setEnabled(True)
        self.parent.refresh_btn.setEnabled(True)

    def write_data_to_log_edit(self, data: str, color: str = "green", align: str = "left") -> None:
        """写入日志

        Args:
            data: 要写入的数据
            color: 文字颜色
            align: 对齐方式
        """
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S.%f")[:-3] + "] "
        html_text = (
            f'<div style="text-align: {align};">'
            f'<span style="color: gray; font-family: Consolas;">{timestamp}</span>'  # Consolas字体
            f'<span style="color: {color}; font-family: Consolas;">{data}</span>'
            f"</div>"
        )
        self.parent.log_edit.append(html_text)
        self.parent.log_edit.verticalScrollBar().setValue(self.parent.log_edit.verticalScrollBar().maximum())
