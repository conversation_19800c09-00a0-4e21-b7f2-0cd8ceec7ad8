r"""
Author: li <EMAIL>
Date: 2024-12-15 22:55:12
LastEditors: li <EMAIL>
LastEditTime: 2025-03-09 22:16:13
FilePath: \单三相电表测试软件\software\serial_fuc.py
"""
from __future__ import annotations
from typing import TYPE_CHECKING
from datetime import datetime
from PySide6.QtWidgets import QMessageBox
from PySide6.QtSerialPort import QSerialPort, QSerialPortInfo
if TYPE_CHECKING:
    from ui文件.主界面_ui import Ui_main_page

class serial_fuc_s:
    def __init__(self, parent : Ui_main_page):
        try:
            if parent is None:
                raise ValueError("parent cannot be None")

            self.parent = parent

            self.serial_port = QSerialPort()

            # 直接连接串口的readyRead信号到数据处理槽函数
            self.serial_port.readyRead.connect(self.process_received_data)

            # 初始刷新串口列表
            self.refresh_serial()

            # 连接按钮信号
            self.parent.refresh_btn.clicked.connect(self.refresh_serial)
            self.parent.open_btn.clicked.connect(self.handle_serial_connection)
            self.parent.set_serial_1_btn.clicked.connect(self.set_serial_115200)        
            self.parent.set_serial_2_btn.clicked.connect(self.set_serial_2400)

        except Exception as e:
            QMessageBox.critical(parent, "错误", f"串口初始化失败: {str(e)}", QMessageBox.Ok)

    def process_received_data(self):
        """处理接收到的数据"""
        if self.serial_port.bytesAvailable() == 0:
            return

        data = self.serial_port.readAll().data()  # 串口接收到的原始数据，直接赋值给 data

        try:
            # 转换为十六进制显示
            hex_data = " ".join([f"{x:02X}" for x in data])

            self.write_data_to_log_edit(f"接收: {hex_data}", "green")

            # 在这里添加数据处理逻辑
            # 例如：解析数据、触发其他操作等

            # 如果需要发送响应
            response = self.parent.protocol_handler.dlt645_parse_response(data)
            if isinstance(response, dict) and len(response) == 3:
                response["cmd_code"] = response["cmd_code"] + 0x80
                response["data"] = b"\x00\x00"
                send_data = self.parent.protocol_handler.dlt645_build_cmd(response)
                self.send_response(send_data)

            elif isinstance(response, str):
                self.write_data_to_log_edit(f"错误类型：{response}", "red")

            else:
                self.write_data_to_log_edit(f"未知类型：{response}", "red")

        except Exception as e:
            self.write_data_to_log_edit(f"数据处理错误: {str(e)}", "red")

    def send_response(self, response):
        """发送响应数据"""
        try:
            if self.serial_port and self.serial_port.isOpen():
                self.serial_port.write(response)
                log_str = "".join([f"{x:02X} " for x in response])
                self.write_data_to_log_edit(f"发送: {log_str}", "blue")
        except Exception as e:
            self.write_data_to_log_edit(f"发送错误: {str(e)}", "red")

    def refresh_serial(self):
        """刷新可用串口列表"""
        port_list = list(QSerialPortInfo.availablePorts())
        self.parent.port_box.clear()
        self.parent.port_box.addItems([port.portName() for port in port_list])

    def open_serial(self):
        """打开串口"""
        try:
            port_name = self.parent.port_box.currentText()
            if not port_name:
                QMessageBox.critical(self.parent, "错误", "请选择串口", QMessageBox.Ok)
                return None

            baud_rate = int(self.parent.band_rate_box.currentText())

            # 配置串口
            self.serial_port.setPortName(port_name)
            self.serial_port.setBaudRate(baud_rate)

            # 数据位映射
            data_len_map = {
                "8位": QSerialPort.Data8,
                "7位": QSerialPort.Data7,
            }
            data_len = data_len_map.get(self.parent.data_len_box.currentText(), QSerialPort.Data8)
            self.serial_port.setDataBits(data_len)

            # 校验位映射
            parity_map = {
                "无校验": QSerialPort.NoParity,
                "奇校验": QSerialPort.OddParity,
                "偶校验": QSerialPort.EvenParity,
            }
            parity = parity_map.get(self.parent.even_box.currentText(), QSerialPort.NoParity)
            self.serial_port.setParity(parity)

            # 停止位映射
            stop_bits_map = {
                "1": QSerialPort.OneStop,
                "2": QSerialPort.TwoStop,
            }
            stop_bits = stop_bits_map.get(self.parent.stop_bits_box.currentText(), QSerialPort.OneStop)
            self.serial_port.setStopBits(stop_bits)

            # 打开串口
            if self.serial_port.open(QSerialPort.ReadWrite):
                return self.serial_port
            else:
                QMessageBox.critical(
                    self.parent,
                    "错误",
                    f"无法打开串口: {self.serial_port.errorString()}",
                    QMessageBox.Ok,
                )
                return None

        except Exception as e:
            QMessageBox.critical(self.parent, "错误", f"打开串口失败: {str(e)}", QMessageBox.Ok)
            return None

    def close_serial(self):
        """关闭串口"""
        if self.serial_port and self.serial_port.isOpen():
            self.serial_port.close()

    def handle_serial_connection(self):
        """处理串口连接，根据当前是开还是关来执行开启或关闭串口"""
        if not self.serial_port.isOpen():
            self.serial_port = self.open_serial()
            if self.serial_port:
                self.parent.open_btn.setText("关闭串口")
                self.disable_serial_controls()
        else:
            self.close_serial()
            self.parent.open_btn.setText("打开串口")
            self.enable_serial_controls()

    def set_serial_115200(self):
        """设置串口1的默认参数"""
        self.parent.band_rate_box.setCurrentText("115200")
        self.parent.data_len_box.setCurrentText("8位")
        self.parent.even_box.setCurrentText("无校验")
        self.parent.stop_bits_box.setCurrentText("1")

    def set_serial_2400(self):
        """设置串口2的默认参数"""
        self.parent.band_rate_box.setCurrentText("2400")
        self.parent.data_len_box.setCurrentText("8位")
        self.parent.even_box.setCurrentText("偶校验")
        self.parent.stop_bits_box.setCurrentText("1")

    def disable_serial_controls(self):
        """禁用串口设置相关控件"""
        self.parent.port_box.setEnabled(False)
        self.parent.band_rate_box.setEnabled(False)
        self.parent.data_len_box.setEnabled(False)
        self.parent.even_box.setEnabled(False)
        self.parent.stop_bits_box.setEnabled(False)
        self.parent.set_serial_1_btn.setEnabled(False)
        self.parent.set_serial_2_btn.setEnabled(False)
        self.parent.refresh_btn.setEnabled(False)

    def enable_serial_controls(self):
        """启用串口设置相关控件"""
        self.parent.port_box.setEnabled(True)
        self.parent.band_rate_box.setEnabled(True)
        self.parent.data_len_box.setEnabled(True)
        self.parent.even_box.setEnabled(True)
        self.parent.stop_bits_box.setEnabled(True)
        self.parent.set_serial_1_btn.setEnabled(True)
        self.parent.set_serial_2_btn.setEnabled(True)
        self.parent.refresh_btn.setEnabled(True)

    def write_data_to_log_edit(self, data, color="green", align="left"):
        """写入日志"""
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S.%f")[:-3] + "] "
        html_text = (
            f'<div style="text-align: {align};">'
            f'<span style="color: gray; font-family: Consolas;">{timestamp}</span>'
            f'<span style="color: {color}; font-family: Consolas;">{data}</span>'
            f"</div>"
        )
        self.parent.log_edit.append(html_text)
        self.parent.log_edit.verticalScrollBar().setValue(self.parent.log_edit.verticalScrollBar().maximum())
