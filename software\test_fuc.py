r"""
Author: li <EMAIL>                # 作者信息及邮箱
Date: 2024-12-30 22:29:15                  # 创建日期
LastEditors: li <EMAIL>           # 最后编辑者信息
LastEditTime: 2025-03-09 22:46:30          # 最后编辑时间
FilePath: \单三相电表测试软件\software\test_fuc.py  # 文件路径
"""

# 冒泡排序算法，用于对数组进行排序
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]


# 测试代码，仅在直接运行时执行，用于测试函数功能
if __name__ == "__main__":

    print("test python")

    # arr = [64, 34, 25, 12, 22, 11, 90]
    # bubble_sort(arr)
    # print("排序后的数组:")
    # for i in range(len(arr)):
    #     print("%d" % arr[i])

    number = 10

    str = f"hello world{number}"
    print(str)

