r"""
Description:
Author: li
Date: 2025-04-19 22:15:32
LastEditors: li
LastEditTime: 2025-04-19 22:15:57
FilePath: \单三相电表测试软件\stacked_widget.py
"""

import sys  # 导入系统模块，用于访问与Python解释器和环境相关的变量和函数
import os  # 导入操作系统模块，用于文件路径操作和系统交互


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "ui文件")))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "software")))


from ui文件.功率校表_ui import Ui_power_calibration
from ui文件.误差校表_ui import Ui_err_calibration
from PySide6.QtWidgets import QWidget, QSizePolicy


class PowerCalibrationWindow(QWidget, Ui_power_calibration):
    def __init__(self, parent=None):
        super().__init__()
        self.setupUi(self)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        if self.layout():
            self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore
            self.layout().setSpacing(0)  # type: ignore
        self.parent_window = parent
        self.pushB_save_params.clicked.connect(self.save_cali_params_to_json)
        self.pushB_freshen_params.clicked.connect(self.get_cali_params_from_json)

    def get_cali_params_from_ui(self) -> dict:
        """从界面获取所有参数并返回字典(使用UI文本作为键)"""
        params = {
            self.windowTitle(): {
                self.label_a.text(): {
                    self.label_v.text(): self.lineEdit_a_v.text(),
                    self.label_i.text(): self.lineEdit_a_i.text(),
                    self.label_p.text(): self.lineEdit_a_p.text(),
                    self.label_q.text(): self.lineEdit_a_q.text(),
                    self.label_s.text(): self.lineEdit_a_s.text(),
                    self.label_z.text(): self.lineEdit_a_z.text(),
                },
                self.label_b.text(): {
                    self.label_v.text(): self.lineEdit_b_v.text(),
                    self.label_i.text(): self.lineEdit_b_i.text(),
                    self.label_p.text(): self.lineEdit_b_p.text(),
                    self.label_q.text(): self.lineEdit_b_q.text(),
                    self.label_s.text(): self.lineEdit_b_s.text(),
                    self.label_z.text(): self.lineEdit_b_z.text(),
                },
                self.label_c.text(): {
                    self.label_v.text(): self.lineEdit_c_v.text(),
                    self.label_i.text(): self.lineEdit_c_i.text(),
                    self.label_p.text(): self.lineEdit_c_p.text(),
                    self.label_q.text(): self.lineEdit_c_q.text(),
                    self.label_s.text(): self.lineEdit_c_s.text(),
                    self.label_z.text(): self.lineEdit_c_z.text(),
                },
            }
        }
        return params

    def apply_params_to_ui(self, data_dict):
        """将字典数据应用到UI控件上（动态属性访问）"""
        try:
            window_data = data_dict.get(self.windowTitle(), {})
            # 相位映射
            phases = {self.label_a.text(): "a", self.label_b.text(): "b", self.label_c.text(): "c"}
            # 参数映射
            params = {
                self.label_v.text(): "v",
                self.label_i.text(): "i",
                self.label_p.text(): "p",
                self.label_q.text(): "q",
                self.label_s.text(): "s",
                self.label_z.text(): "z",
            }

            # 遍历相位
            for phase_label, phase_data in window_data.items():
                # 遍历参数
                for param_label, value in phase_data.items():
                    # 动态构造控件名称
                    widget_name = f"lineEdit_{phases[phase_label]}_{params[param_label]}"

                    # 如果控件存在，设置其值
                    if hasattr(self, widget_name):
                        widget = getattr(self, widget_name)
                        widget.setText(str(value))

            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit("参数已应用到界面")
            else:
                print("参数已应用到界面")
        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"应用参数失败: {str(e)}", "red")
            else:
                print(f"应用参数失败: {str(e)}")

    def save_cali_params_to_json(self):
        """将参数保存到txt文件"""
        if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
            self.parent_window.display_to_log_edit("参数已保存到json文件")
        else:
            print("参数已保存到json文件")

    def get_cali_params_from_json(self):
        """从json文件中设置参数到界面"""
        cali_dict = self.get_cali_params_from_ui()
        if self.parent_window and hasattr(self.parent_window, "file_handler"):
            cali_dict = self.parent_window.file_handler.load_json_data(type_dict=cali_dict)
        else:
            print("从json文件中读取参数失败")

        self.apply_params_to_ui(cali_dict)


# 自定义误差校表窗口类
class ErrorCalibrationWindow(QWidget, Ui_err_calibration):
    def __init__(self, parent=None):
        super().__init__()
        self.setupUi(self)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        if self.layout():
            self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore
            self.layout().setSpacing(0)  # type: ignore
        self.parent_window = parent

    def get_cali_params_from_ui(self):
        """从界面获取所有参数并返回字典(使用UI文本作为键)"""
        params = {
            self.windowTitle(): {
                # self.label_a.text(): {
                #     self.label_v.text(): self.lineEdit_a_v.text(),
                #     self.label_i.text(): self.lineEdit_a_i.text(),
                #     self.label_p.text(): self.lineEdit_a_p.text(),
                #     self.label_q.text(): self.lineEdit_a_q.text(),
                #     self.label_s.text(): self.lineEdit_a_s.text(),
                #     self.label_z.text(): self.lineEdit_a_z.text(),
                # },
                # self.label_b.text(): {
                #     self.label_v.text(): self.lineEdit_b_v.text(),
                #     self.label_i.text(): self.lineEdit_b_i.text(),
                #     self.label_p.text(): self.lineEdit_b_p.text(),
                #     self.label_q.text(): self.lineEdit_b_q.text(),
                #     self.label_s.text(): self.lineEdit_b_s.text(),
                #     self.label_z.text(): self.lineEdit_b_z.text(),
                # },
                # self.label_c.text(): {
                #     self.label_v.text(): self.lineEdit_c_v.text(),
                #     self.label_i.text(): self.lineEdit_c_i.text(),
                #     self.label_p.text(): self.lineEdit_c_p.text(),
                #     self.label_q.text(): self.lineEdit_c_q.text(),
                #     self.label_s.text(): self.lineEdit_c_s.text(),
                #     self.label_z.text(): self.lineEdit_c_z.text(),
                # },
            }
        }
        return params

    def apply_params_to_ui(self, data_dict):
        """将字典数据应用到UI控件上（动态属性访问）"""
        try:
            window_data = data_dict.get(self.windowTitle(), {})
            # # 相位映射
            # phases = {self.label_a.text(): "a", self.label_b.text(): "b", self.label_c.text(): "c"}
            # # 参数映射
            # params = {
            #     self.label_v.text(): "v",
            #     self.label_i.text(): "i",
            #     self.label_p.text(): "p",
            #     self.label_q.text(): "q",
            #     self.label_s.text(): "s",
            #     self.label_z.text(): "z",
            # }
            # 遍历相位
            # for phase_label, phase_data in window_data.items():
            #     # 遍历参数
            #     for param_label, value in phase_data.items():
            #         # 动态构造控件名称
            #         widget_name = f"lineEdit_{phases[phase_label]}_{params[param_label]}"
            #         # 如果控件存在，设置其值
            #         if hasattr(self, widget_name):
            #             widget = getattr(self, widget_name)
            #             widget.setText(str(value))
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit("参数已应用到界面")
            else:
                print("参数已应用到界面")
        except Exception as e:
            if self.parent_window and hasattr(self.parent_window, "display_to_log_edit"):
                self.parent_window.display_to_log_edit(f"应用参数失败: {str(e)}", "red")
            else:
                print(f"应用参数失败: {str(e)}")

    def save_cali_params_to_txt(self):
        """将参数保存到txt文件"""
        if self.parent_window and hasattr(self.parent_window, "file_handler"):
            self.parent_window.file_handler.save_json_data(self.get_cali_params_from_ui())

    def get_cali_params_from_json(self):
        """从json文件中设置参数到界面"""
        cali_dict = self.get_cali_params_from_ui()
        if self.parent_window and hasattr(self.parent_window, "file_handler"):
            cali_dict = self.parent_window.file_handler.load_json_data(type_dict=cali_dict)
        self.apply_params_to_ui(cali_dict)
